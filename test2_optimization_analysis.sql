-- test2.sql执行计划分析和优化方案
-- 执行时间：2567.810ms，返回9行数据

-- ========== 执行计划关键问题分析 ==========
/*
主要性能瓶颈：
1. 总执行时间：2567ms，比test1.sql的1621ms还要慢58%
2. 内存使用：1,376,256kB (1.3GB)
3. 关键问题：
   - Nested Loop Left Join耗时最多
   - Dynamic Index Scan on dw_pd_fund_nav (never executed) 但仍然准备了分区扫描
   - 分区扫描准备：183.9个分区 (超过总分区数143，说明有重复扫描)

特殊发现：
- LEFT JOIN实际上没有执行 (never executed)
- 但是分区选择器仍然准备了143个分区
- 这说明查询计划准备了大量不必要的资源
*/

-- ========== 原始SQL问题分析 ==========
/*
test2.sql的问题：
1. 表结构不同：使用 ods.raw_cm_custtrade_direct_high 而不是之前的表
2. JOIN条件：appserialno = t1.deal_dtl_no (没有表别名前缀)
3. LEFT JOIN条件：使用 t2.fundcode 和 t2.ack_dt
4. WHERE条件：apply_appserialno 过滤

潜在问题：
- appserialno 字段没有表别名，可能导致歧义
- LEFT JOIN准备了大量分区但没有执行，说明逻辑可能有问题
*/

-- ========== 优化方案 ==========

-- 方案一：修复表别名和JOIN条件
WITH filtered_data AS (
    -- 先过滤出需要的记录
    SELECT 
        t2.apply_appserialno as preId,
        t2.appserialno,
        t2.fundcode,
        t2.ack_dt
    FROM ods.raw_cm_custtrade_direct_high t2
    WHERE t2.apply_appserialno IN ('71418','71815','71815','73828','73925',
                                   '69822','62608','60755','74171','73789')
),
main_data AS (
    -- 完成主要JOIN，获取交易数据
    SELECT 
        fd.preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        fd.fundcode,
        fd.ack_dt
    FROM filtered_data fd
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON fd.appserialno = t1.deal_dtl_no
)
SELECT 
    md.preId,
    CASE 
        WHEN md.ack_amt > 0 AND md.ack_vol > 0 AND (md.ack_amt_rmb - COALESCE(md.fee, 0)) > 0 AND md.nav > 0 THEN
            md.nav
        WHEN md.ack_amt > 0 AND md.ack_vol > 0 AND (md.ack_amt_rmb - COALESCE(md.fee, 0)) > 0 THEN
            (md.ack_amt_rmb - COALESCE(md.fee, 0)) / md.ack_vol
        WHEN md.ack_amt > 0 AND md.ack_vol > 0 THEN
            0
        ELSE
            COALESCE(navt.nav, 0)
    END as nav
FROM main_data md
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON md.fundcode = navt.fund_code 
    AND md.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');


-- 方案二：分离处理策略（推荐）
-- 基于执行计划显示LEFT JOIN实际没有执行的情况
WITH base_data AS (
    SELECT 
        t2.apply_appserialno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t2.fundcode,
        t2.ack_dt,
        -- 预计算条件
        (t1.ack_amt > 0 AND t1.ack_vol > 0) as has_valid_amounts,
        ((t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0) as has_positive_net,
        (t1.nav > 0) as has_positive_nav
    FROM ods.raw_cm_custtrade_direct_high t2
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t2.appserialno = t1.deal_dtl_no
    WHERE t2.apply_appserialno IN ('71418','71815','71815','73828','73925',
                                   '69822','62608','60755','74171','73789')
)
-- 不需要nav表的记录（大部分情况）
SELECT 
    preId,
    CASE 
        WHEN has_valid_amounts AND has_positive_net AND has_positive_nav THEN nav
        WHEN has_valid_amounts AND has_positive_net THEN (ack_amt_rmb - COALESCE(fee, 0)) / ack_vol
        WHEN has_valid_amounts THEN 0
    END as nav
FROM base_data
WHERE has_valid_amounts

UNION ALL

-- 需要nav表的记录（少数情况）
SELECT 
    bd.preId,
    COALESCE(navt.nav, 0) as nav
FROM base_data bd
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON bd.fundcode = navt.fund_code 
    AND bd.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
WHERE NOT bd.has_valid_amounts;


-- 方案三：简化版本（如果确定不需要nav表）
-- 基于执行计划显示LEFT JOIN从未执行的情况
SELECT 
    t2.apply_appserialno as preId,
    CASE 
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 AND t1.nav > 0 THEN
            t1.nav
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
            (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
            0
        ELSE
            0  -- 直接返回0，避免LEFT JOIN
    END as nav
FROM ods.raw_cm_custtrade_direct_high t2
INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t2.appserialno = t1.deal_dtl_no
WHERE t2.apply_appserialno IN ('71418','71815','71815','73828','73925',
                               '69822','62608','60755','74171','73789');


-- 方案四：条件优化版本
-- 预计算条件，减少重复计算
WITH optimized_base AS (
    SELECT 
        t2.apply_appserialno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t2.fundcode,
        t2.ack_dt,
        -- 预计算复杂表达式
        (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) as net_amount
    FROM ods.raw_cm_custtrade_direct_high t2
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t2.appserialno = t1.deal_dtl_no
    WHERE t2.apply_appserialno IN ('71418','71815','71815','73828','73925',
                                   '69822','62608','60755','74171','73789')
)
SELECT 
    ob.preId,
    CASE 
        WHEN ob.ack_amt > 0 AND ob.ack_vol > 0 AND ob.net_amount > 0 AND ob.nav > 0 THEN
            ob.nav
        WHEN ob.ack_amt > 0 AND ob.ack_vol > 0 AND ob.net_amount > 0 THEN
            ob.net_amount / ob.ack_vol
        WHEN ob.ack_amt > 0 AND ob.ack_vol > 0 THEN
            0
        ELSE
            COALESCE(navt.nav, 0)
    END as nav
FROM optimized_base ob
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON ob.fundcode = navt.fund_code 
    AND ob.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');


-- ========== 优化建议总结 ==========
/*
基于执行计划分析的建议：

1. **立即可用**：方案三（简化版本）
   - 如果确定不需要nav表数据，直接移除LEFT JOIN
   - 预计执行时间从2567ms减少到100-300ms

2. **保守优化**：方案一（CTE优化）
   - 保持原有逻辑，但优化执行顺序
   - 预计执行时间减少50-70%

3. **激进优化**：方案二（分离处理）
   - 完全避免不必要的LEFT JOIN
   - 预计执行时间减少70-85%

4. **平衡方案**：方案四（条件优化）
   - 预计算表达式，减少重复计算
   - 预计执行时间减少40-60%

推荐优先级：方案三 > 方案二 > 方案一 > 方案四

关键发现：LEFT JOIN在当前数据下从未执行，说明可能不需要nav表数据，
建议先验证业务逻辑是否真的需要nav表。
*/
