-- 优化后的SQL查询及建议的索引
-- 数据量：ods.raw_high_deal_order_dtl_high(40万) + ods.raw_cm_zt_orderinfo(20万) + dw.dw_trade_mid_ack_gd(40万)

-- ========== 建议创建的索引 ==========
-- 这些索引将显著提升查询性能

-- 1. 为appointmentdealno字段创建索引（最重要）
-- CREATE INDEX idx_raw_cm_zt_orderinfo_appointmentdealno ON ods.raw_cm_zt_orderinfo(appointmentdealno);

-- 2. 为deal_no字段创建索引
-- CREATE INDEX idx_raw_high_deal_order_dtl_high_deal_no ON ods.raw_high_deal_order_dtl_high(deal_no);

-- 3. 为fund_code和ack_dt创建复合索引
-- CREATE INDEX idx_raw_high_deal_order_dtl_high_fund_ack ON ods.raw_high_deal_order_dtl_high(fund_code, ack_dt);

-- 4. 为dw_pd_fund_nav表的查询条件创建索引（如果不存在）
-- CREATE INDEX idx_dw_pd_fund_nav_fund_trade_dt ON dw.dw_pd_fund_nav(fund_code, trade_dt);

-- ========== 优化后的SQL查询 ==========

WITH filtered_orderinfo AS (
    -- 先过滤出需要的appointmentdealno，大幅减少后续JOIN的数据量
    -- 从20万条记录过滤到只有10条记录
    SELECT dealno, appointmentdealno
    FROM ods.raw_cm_zt_orderinfo 
    WHERE appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                               '3286078','3355240','3352174','3286123','3341731')
),
main_data AS (
    -- 主要数据关联，优化JOIN顺序：小表驱动大表
    SELECT 
        t4.appointmentdealno as preId,
        t1.ack_amt,
        t1.ack_vol, 
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav as t1_nav,
        t3.fund_code,
        t3.ack_dt
    FROM filtered_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
)
SELECT 
    md.preId,
    CASE 
        WHEN md.ack_amt > 0 AND md.ack_vol > 0 THEN
            CASE 
                WHEN (md.ack_amt_rmb - COALESCE(md.fee, 0)) > 0 THEN
                    COALESCE(
                        NULLIF(md.t1_nav, 0),  -- 使用NULLIF简化逻辑，避免嵌套CASE
                        (md.ack_amt_rmb - COALESCE(md.fee, 0)) / md.ack_vol
                    )
                ELSE 0 
            END
        ELSE navt.nav
    END as nav
FROM main_data md
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON md.fund_code = navt.fund_code 
    AND md.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');

-- ========== 性能优化说明 ==========
/*
主要优化点：

1. **过滤条件前置**：
   - 使用CTE先过滤ods.raw_cm_zt_orderinfo表，从20万条记录减少到10条
   - 大幅减少后续JOIN操作的数据量

2. **JOIN顺序优化**：
   - 先用小结果集（10条）驱动大表
   - 利用现有的主键索引pk_trade_mid_ack_gd进行高效JOIN

3. **逻辑简化**：
   - 使用NULLIF替代复杂的CASE WHEN判断
   - 减少重复的表达式计算

4. **索引建议**：
   - appointmentdealno索引：将过滤操作从全表扫描变为索引查找
   - deal_no索引：优化第一个JOIN操作
   - 复合索引：优化LEFT JOIN操作

5. **预期性能提升**：
   - 查询时间预计减少60-80%
   - 减少磁盘I/O和内存使用
   - 提高并发查询能力

注意：请在非生产环境先测试索引创建和查询性能
*/
