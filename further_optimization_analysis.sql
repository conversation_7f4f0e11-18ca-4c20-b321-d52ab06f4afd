-- 基于优化版本执行计划的进一步优化分析
-- 当前执行时间：1621.245ms，仍有优化空间

-- ========== 执行计划瓶颈分析 ==========
/*
主要性能瓶颈：
1. Dynamic Index Scan on dw_pd_fund_nav: 158.569..521.564ms per loop (3 loops)
   - 总耗时：~1565ms (占96.5%)
   - 分区扫描：143个分区，每次扫描68.1个分区
   - 仍然是最大瓶颈

2. Broadcast Motion: 577.278..1560.305ms
   - 数据广播开销很大

3. GroupAggregate + Sort: 1610.466..1610.528ms
   - 排序和聚合操作耗时

4. 内存使用：1,376,256kB (1.3GB) 仍然很高
*/

-- ========== 进一步优化方案 ==========

-- 方案一：避免分区扫描，使用条件过滤
-- 如果知道ack_dt的范围，可以添加日期范围过滤
WITH filtered_data AS (
    SELECT 
        t4.appointmentdealno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t3.fund_code,
        t3.ack_dt
    FROM ods.raw_cm_zt_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
    WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                                  '3286078','3355240','3352174','3286123','3341731')
),
-- 关键优化：添加日期范围过滤，减少分区扫描
nav_data_optimized AS (
    SELECT DISTINCT
        navt.fund_code,
        TO_CHAR(navt.trade_dt, 'YYYYMMDD') as ack_dt,
        navt.nav
    FROM dw.dw_pd_fund_nav navt
    WHERE navt.fund_code IN (SELECT DISTINCT fund_code FROM filtered_data)
      AND navt.trade_dt >= (SELECT DATE(MIN(ack_dt)) FROM filtered_data)  -- 添加日期范围
      AND navt.trade_dt <= (SELECT DATE(MAX(ack_dt)) FROM filtered_data)  -- 减少分区扫描
)
SELECT 
    fd.preId,
    CASE 
        WHEN fd.ack_amt > 0 AND fd.ack_vol > 0 AND (fd.ack_amt_rmb - COALESCE(fd.fee, 0)) > 0 AND fd.nav > 0 THEN
            fd.nav
        WHEN fd.ack_amt > 0 AND fd.ack_vol > 0 AND (fd.ack_amt_rmb - COALESCE(fd.fee, 0)) > 0 THEN 
            (fd.ack_amt_rmb - COALESCE(fd.fee, 0)) / fd.ack_vol
        WHEN fd.ack_amt > 0 AND fd.ack_vol > 0 THEN 
            0
        ELSE 
            COALESCE(nd.nav, 0)
    END as nav
FROM filtered_data fd
LEFT JOIN nav_data_optimized nd ON fd.fund_code = nd.fund_code AND fd.ack_dt = nd.ack_dt;


-- 方案二：简化逻辑，减少计算复杂度
-- 预计算所有条件，避免重复计算
WITH base_data AS (
    SELECT 
        t4.appointmentdealno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t3.fund_code,
        t3.ack_dt,
        -- 预计算布尔条件，减少CASE表达式复杂度
        (t1.ack_amt > 0 AND t1.ack_vol > 0) as has_valid_amounts,
        ((t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0) as has_positive_net,
        (t1.nav > 0) as has_positive_nav,
        (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) as net_amount
    FROM ods.raw_cm_zt_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
    WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                                  '3286078','3355240','3352174','3286123','3341731')
)
SELECT 
    bd.preId,
    CASE 
        WHEN bd.has_valid_amounts AND bd.has_positive_net AND bd.has_positive_nav THEN bd.nav
        WHEN bd.has_valid_amounts AND bd.has_positive_net THEN bd.net_amount / bd.ack_vol
        WHEN bd.has_valid_amounts THEN 0
        ELSE COALESCE(navt.nav, 0)
    END as nav
FROM base_data bd
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON bd.fund_code = navt.fund_code 
    AND bd.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');


-- 方案三：分离计算，只对需要的记录查询nav表
-- 基于大部分记录可能不需要查询nav表的假设
WITH main_calculation AS (
    SELECT 
        t4.appointmentdealno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t3.fund_code,
        t3.ack_dt,
        CASE 
            WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 AND t1.nav > 0 THEN
                t1.nav
            WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN 
                (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
            WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN 
                0
            ELSE 
                NULL  -- 标记需要查询nav表
        END as calculated_nav,
        -- 标记是否需要查询nav表
        CASE WHEN t1.ack_amt <= 0 OR t1.ack_vol <= 0 THEN 1 ELSE 0 END as need_nav_lookup
    FROM ods.raw_cm_zt_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
    WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                                  '3286078','3355240','3352174','3286123','3341731')
)
SELECT 
    mc.preId,
    CASE 
        WHEN mc.need_nav_lookup = 0 THEN mc.calculated_nav
        ELSE COALESCE(navt.nav, 0)
    END as nav
FROM main_calculation mc
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON mc.need_nav_lookup = 1  -- 只对需要的记录进行JOIN
    AND mc.fund_code = navt.fund_code 
    AND mc.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');


-- 方案四：使用UNION ALL分离处理
-- 将需要和不需要nav表的记录分开处理
WITH base_data AS (
    SELECT 
        t4.appointmentdealno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t3.fund_code,
        t3.ack_dt
    FROM ods.raw_cm_zt_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
    WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                                  '3286078','3355240','3352174','3286123','3341731')
)
-- 不需要nav表的记录
SELECT 
    preId,
    CASE 
        WHEN ack_amt > 0 AND ack_vol > 0 AND (ack_amt_rmb - COALESCE(fee, 0)) > 0 AND nav > 0 THEN nav
        WHEN ack_amt > 0 AND ack_vol > 0 AND (ack_amt_rmb - COALESCE(fee, 0)) > 0 THEN (ack_amt_rmb - COALESCE(fee, 0)) / ack_vol
        WHEN ack_amt > 0 AND ack_vol > 0 THEN 0
    END as nav
FROM base_data
WHERE ack_amt > 0 AND ack_vol > 0

UNION ALL

-- 需要nav表的记录
SELECT 
    bd.preId,
    COALESCE(navt.nav, 0) as nav
FROM base_data bd
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON bd.fund_code = navt.fund_code 
    AND bd.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
WHERE bd.ack_amt <= 0 OR bd.ack_vol <= 0;


-- ========== 推荐优化策略 ==========
/*
基于执行计划分析，推荐按以下优先级优化：

1. **方案一（最推荐）**：添加日期范围过滤
   - 可以显著减少分区扫描范围
   - 预计减少50-70%的扫描时间

2. **方案二**：预计算条件
   - 简化CASE表达式
   - 减少重复计算开销

3. **方案三**：条件性JOIN
   - 只对真正需要的记录查询nav表
   - 适用于大部分记录不需要nav的场景

4. **方案四**：分离处理
   - 完全避免不必要的LEFT JOIN
   - 最大化性能，但逻辑稍复杂

预期效果：
- 执行时间从1621ms减少到300-600ms
- 内存使用减少40-60%
- 分区扫描效率提升70%+
*/
