-- 基于执行计划分析的优化SQL
-- 执行计划分析发现的问题：
-- 1. 总执行时间：1908.594ms，返回10行数据
-- 2. 主要瓶颈：LEFT JOIN dw_pd_fund_nav 耗时最多（169.351..584.538 rows=1 loops=3）
-- 3. 分区扫描：143个分区，每次循环扫描68.1个分区
-- 4. 内存使用：1376256kB (1.3GB)

-- ========== 优化方案一：减少LEFT JOIN的循环次数 ==========
-- 核心思路：先完成主要数据JOIN，最后一次性处理LEFT JOIN

WITH filtered_data AS (
    -- 第一步：完成主要的INNER JOIN，获取所有需要的基础数据
    SELECT 
        t4.appointmentdealno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t3.fund_code,
        t3.ack_dt
    FROM ods.raw_cm_zt_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
    WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                                  '3286078','3355240','3352174','3286123','3341731')
),
nav_data AS (
    -- 第二步：预先获取所有可能需要的nav数据，减少分区扫描
    SELECT DISTINCT
        fd.fund_code,
        fd.ack_dt,
        navt.nav
    FROM filtered_data fd
    INNER JOIN dw.dw_pd_fund_nav navt 
        ON fd.fund_code = navt.fund_code 
        AND fd.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
)
-- 第三步：最终结果计算，避免重复的LEFT JOIN
SELECT 
    fd.preId,
    CASE 
        WHEN fd.ack_amt > 0 AND fd.ack_vol > 0 AND (fd.ack_amt_rmb - COALESCE(fd.fee, 0)) > 0 AND fd.nav > 0 THEN
            fd.nav
        WHEN fd.ack_amt > 0 AND fd.ack_vol > 0 AND (fd.ack_amt_rmb - COALESCE(fd.fee, 0)) > 0 THEN 
            (fd.ack_amt_rmb - COALESCE(fd.fee, 0)) / fd.ack_vol
        WHEN fd.ack_amt > 0 AND fd.ack_vol > 0 THEN 
            0
        ELSE 
            COALESCE(nd.nav, 0)  -- 使用COALESCE提供默认值
    END as nav
FROM filtered_data fd
LEFT JOIN nav_data nd ON fd.fund_code = nd.fund_code AND fd.ack_dt = nd.ack_dt;


-- ========== 优化方案二：条件前置，减少计算量 ==========
-- 基于执行计划显示大部分记录不满足条件的情况

WITH base_data AS (
    SELECT 
        t4.appointmentdealno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t3.fund_code,
        t3.ack_dt,
        -- 预计算条件，避免重复计算
        CASE 
            WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN 1 
            ELSE 0 
        END as has_valid_amt_vol,
        CASE 
            WHEN (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN 1 
            ELSE 0 
        END as has_positive_net_amt
    FROM ods.raw_cm_zt_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
    WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                                  '3286078','3355240','3352174','3286123','3341731')
)
SELECT 
    bd.preId,
    CASE 
        WHEN bd.has_valid_amt_vol = 1 AND bd.has_positive_net_amt = 1 AND bd.nav > 0 THEN
            bd.nav
        WHEN bd.has_valid_amt_vol = 1 AND bd.has_positive_net_amt = 1 THEN 
            (bd.ack_amt_rmb - COALESCE(bd.fee, 0)) / bd.ack_vol
        WHEN bd.has_valid_amt_vol = 1 THEN 
            0
        ELSE 
            COALESCE(navt.nav, 0)
    END as nav
FROM base_data bd
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON bd.fund_code = navt.fund_code 
    AND bd.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');


-- ========== 优化方案三：分步执行，避免复杂JOIN ==========
-- 当LEFT JOIN成为瓶颈时，考虑分步处理

-- 第一步：获取主要数据
WITH main_result AS (
    SELECT 
        t4.appointmentdealno as preId,
        CASE 
            WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 AND t1.nav > 0 THEN
                t1.nav
            WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN 
                (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
            WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN 
                0
            ELSE 
                NULL  -- 标记需要从nav表获取
        END as nav,
        t3.fund_code,
        t3.ack_dt,
        -- 标记是否需要查询nav表
        CASE 
            WHEN t1.ack_amt <= 0 OR t1.ack_vol <= 0 THEN 1 
            ELSE 0 
        END as need_nav_lookup
    FROM ods.raw_cm_zt_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
    WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                                  '3286078','3355240','3352174','3286123','3341731')
)
-- 第二步：只对需要的记录进行LEFT JOIN
SELECT 
    mr.preId,
    CASE 
        WHEN mr.need_nav_lookup = 0 THEN mr.nav
        ELSE COALESCE(navt.nav, 0)
    END as nav
FROM main_result mr
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON mr.need_nav_lookup = 1  -- 只对需要的记录进行JOIN
    AND mr.fund_code = navt.fund_code 
    AND mr.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');


-- ========== 性能优化总结 ==========
/*
基于执行计划的关键发现：

1. **主要瓶颈**：
   - LEFT JOIN dw_pd_fund_nav 每次循环耗时584ms
   - 分区扫描143个分区，效率低下
   - 内存使用1.3GB过高

2. **优化策略**：
   - 减少LEFT JOIN的循环次数
   - 预计算条件，避免重复计算
   - 条件前置，减少不必要的数据处理
   - 分步执行，只对需要的记录进行复杂JOIN

3. **预期效果**：
   - 执行时间从1908ms减少到500-800ms
   - 内存使用减少60-70%
   - 分区扫描次数显著减少

推荐使用方案一，它在保持逻辑清晰的同时最大化减少了LEFT JOIN的性能开销。
*/
