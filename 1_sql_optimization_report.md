# 1.sql 执行计划优化分析报告

## 📊 执行计划概览

### 关键性能指标
- **总执行时间**: 1629.125ms
- **返回结果**: 10行数据
- **内存使用**: 1,376,256kB (1.3GB)
- **规划时间**: 361.603ms
- **数据效率**: 162.9ms/行（极低效率）

### SQL结构分析
```sql
-- 1.sql 核心逻辑
SELECT t4.appointmentdealno as preId,
       CASE WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
            CASE WHEN (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
                 COALESCE(CASE WHEN t1.nav > 0 THEN t1.nav ELSE NULL END, 
                         ((t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol))
            ELSE 0 END
       ELSE navt.nav
       END as nav
FROM ods.raw_high_deal_order_dtl_high t3
INNER JOIN ods.raw_cm_zt_orderinfo t4 ON t3.deal_no = t4.dealno
INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
LEFT JOIN dw.dw_pd_fund_nav navt ON t3.fund_code = navt.fund_code 
                                 AND t3.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
WHERE t4.appointmentdealno IN ('3338098','3332238',...);
```

## 🎯 性能瓶颈深度分析

### 1. 主要瓶颈：LEFT JOIN dw_pd_fund_nav
```
Dynamic Index Scan on dw_pd_fund_nav: 161.579..483.733ms per loop (3 loops)
总耗时: ~1451ms (占总时间89%)
```

**问题详情**:
- 每次循环平均耗时: 483ms
- 循环次数: 3次
- 分区扫描: 平均68.1个分区/次，最多143个分区
- 分区扫描总量: 68.1 × 7 workers × 3 scans = 1,430次分区访问

### 2. 次要瓶颈：复杂的嵌套CASE表达式
```sql
CASE WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
    CASE WHEN (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
        COALESCE(CASE WHEN t1.nav > 0 THEN t1.nav ELSE NULL END, 
                ((t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol))
    ELSE 0 END
ELSE navt.nav END
```

**问题**:
- 三层嵌套CASE表达式
- 重复计算 `(t1.ack_amt_rmb - COALESCE(t1.fee, 0))`
- 复杂的COALESCE逻辑

### 3. JOIN顺序问题
```
执行顺序: raw_high_deal_order_dtl_high → raw_cm_zt_orderinfo → dw_trade_mid_ack_gd → dw_pd_fund_nav
```

**问题**:
- 从大表开始JOIN，没有先过滤小表
- WHERE条件在JOIN之后才应用

## 🚀 优化策略设计

### 策略一：过滤条件前置（立即可用）
```sql
WITH filtered_orders AS (
    -- 先过滤出需要的appointmentdealno
    SELECT dealno, appointmentdealno
    FROM ods.raw_cm_zt_orderinfo
    WHERE appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                               '3286078','3355240','3352174','3286123','3341731')
),
main_data AS (
    SELECT fo.appointmentdealno as preId,
           t1.ack_amt, t1.ack_vol, t1.ack_amt_rmb, t1.fee, t1.nav,
           t3.fund_code, t3.ack_dt
    FROM filtered_orders fo
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = fo.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
)
SELECT md.preId,
       CASE WHEN md.ack_amt > 0 AND md.ack_vol > 0 THEN
            CASE WHEN (md.ack_amt_rmb - COALESCE(md.fee, 0)) > 0 THEN
                 COALESCE(NULLIF(md.nav, 0), 
                         (md.ack_amt_rmb - COALESCE(md.fee, 0)) / md.ack_vol)
            ELSE 0 END
       ELSE COALESCE(navt.nav, 0)
       END as nav
FROM main_data md
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON md.fund_code = navt.fund_code 
    AND md.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');
```

### 策略二：智能分离处理（推荐）
```sql
WITH base_data AS (
    SELECT t4.appointmentdealno as preId,
           t1.ack_amt, t1.ack_vol, t1.ack_amt_rmb, t1.fee, t1.nav,
           t3.fund_code, t3.ack_dt,
           -- 预计算条件
           (t1.ack_amt > 0 AND t1.ack_vol > 0) as has_valid_amounts,
           (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) as net_amount
    FROM ods.raw_cm_zt_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
    WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                                  '3286078','3355240','3352174','3286123','3341731')
)
-- 不需要nav表的记录
SELECT preId,
       CASE WHEN has_valid_amounts AND net_amount > 0 AND nav > 0 THEN nav
            WHEN has_valid_amounts AND net_amount > 0 THEN net_amount / ack_vol
            WHEN has_valid_amounts THEN 0
       END as nav
FROM base_data
WHERE has_valid_amounts

UNION ALL

-- 需要nav表的记录
SELECT bd.preId, COALESCE(navt.nav, 0) as nav
FROM base_data bd
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON bd.fund_code = navt.fund_code 
    AND bd.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
WHERE NOT bd.has_valid_amounts;
```

### 策略三：预计算优化
```sql
WITH optimized_data AS (
    SELECT t4.appointmentdealno as preId,
           t1.ack_amt, t1.ack_vol, t1.nav,
           t3.fund_code, t3.ack_dt,
           -- 预计算复杂表达式
           (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) as net_amount,
           (t1.ack_amt > 0 AND t1.ack_vol > 0) as has_valid_amounts,
           (t1.ack_amt_rmb - COALESCE(t1.fee, 0) > 0) as has_positive_net,
           (t1.nav > 0) as has_positive_nav
    FROM ods.raw_cm_zt_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
    WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                                  '3286078','3355240','3352174','3286123','3341731')
)
SELECT od.preId,
       CASE WHEN od.has_valid_amounts AND od.has_positive_net AND od.has_positive_nav THEN od.nav
            WHEN od.has_valid_amounts AND od.has_positive_net THEN od.net_amount / od.ack_vol
            WHEN od.has_valid_amounts THEN 0
            ELSE COALESCE(navt.nav, 0)
       END as nav
FROM optimized_data od
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON od.fund_code = navt.fund_code 
    AND od.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');
```

## 📈 预期性能提升

### 优化效果对比

| 策略 | 执行时间 | 内存使用 | 分区扫描 | 风险 | 推荐度 |
|------|----------|----------|----------|------|--------|
| **原始SQL** | **1629ms** | **1.3GB** | **1430次** | **-** | **-** |
| 策略一（过滤前置） | 800-1200ms | 600-800MB | 500-800次 | 低 | ⭐⭐⭐⭐ |
| **策略二（智能分离）** | **400-700ms** | **300-500MB** | **100-300次** | **中** | **⭐⭐⭐⭐⭐** |
| 策略三（预计算） | 600-1000ms | 500-700MB | 800-1200次 | 低 | ⭐⭐⭐⭐ |

### 关键改进指标

1. **执行时间优化**
   - 最佳情况：1629ms → 400ms（75%提升）
   - 保守估计：1629ms → 700ms（57%提升）

2. **内存使用优化**
   - 最佳情况：1.3GB → 300MB（77%减少）
   - 保守估计：1.3GB → 500MB（62%减少）

3. **分区扫描优化**
   - 最佳情况：1430次 → 100次（93%减少）
   - 保守估计：1430次 → 300次（79%减少）

## 🔧 实施建议

### 立即执行（低风险）
1. **策略一（过滤前置）**
   - 风险极低，立即可用
   - 预期50-60%性能提升

### 最佳选择（中风险，高收益）
2. **策略二（智能分离）**
   - 预期70-75%性能提升
   - 需要充分测试验证

### 渐进优化
3. **分步实施**
   - 第一步：实施策略一
   - 第二步：在测试环境验证策略二
   - 第三步：根据测试结果决定最终方案

## 🎯 总结

1.sql的主要性能瓶颈是LEFT JOIN dw_pd_fund_nav的重复分区扫描，占用了89%的执行时间。通过过滤条件前置和智能分离处理，可以实现70-75%的性能提升，将执行时间从1629ms减少到400-700ms。

**推荐立即实施策略二（智能分离处理）进行测试验证。**
