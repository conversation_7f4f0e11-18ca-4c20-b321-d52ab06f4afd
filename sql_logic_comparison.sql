-- test1.sql中两条SQL的逻辑对比分析

-- ========== 第一条SQL的逻辑 ==========
/*
条件判断逻辑：
1. 如果 ack_amt > 0 AND ack_vol > 0 AND (ack_amt_rmb - fee) > 0 AND nav > 0
   → 返回 t1.nav

2. 否则如果 ack_amt > 0 AND ack_vol > 0 AND (ack_amt_rmb - fee) > 0 
   → 返回 (ack_amt_rmb - fee) / ack_vol

3. 否则如果 ack_amt > 0 AND ack_vol > 0
   → 返回 0

4. 否则
   → 返回 navt.nav
*/

-- ========== 第二条SQL的逻辑 ==========
/*
条件判断逻辑：
1. 如果 ack_amt > 0 AND ack_vol > 0
   1.1 如果 (ack_amt_rmb - fee) > 0
       1.1.1 如果 nav > 0 → 返回 t1.nav
       1.1.2 否则 → 返回 (ack_amt_rmb - fee) / ack_vol
   1.2 否则 → 返回 0

2. 否则
   → 返回 navt.nav
*/

-- ========== 逻辑等价性分析 ==========

-- 测试用例1：ack_amt > 0, ack_vol > 0, (ack_amt_rmb - fee) > 0, nav > 0
-- 第一条SQL：返回 t1.nav
-- 第二条SQL：返回 t1.nav
-- 结果：一致 ✓

-- 测试用例2：ack_amt > 0, ack_vol > 0, (ack_amt_rmb - fee) > 0, nav <= 0
-- 第一条SQL：返回 (ack_amt_rmb - fee) / ack_vol
-- 第二条SQL：返回 (ack_amt_rmb - fee) / ack_vol
-- 结果：一致 ✓

-- 测试用例3：ack_amt > 0, ack_vol > 0, (ack_amt_rmb - fee) <= 0
-- 第一条SQL：返回 0
-- 第二条SQL：返回 0
-- 结果：一致 ✓

-- 测试用例4：ack_amt <= 0 OR ack_vol <= 0
-- 第一条SQL：返回 navt.nav
-- 第二条SQL：返回 navt.nav
-- 结果：一致 ✓

-- ========== 结论 ==========
-- 两条SQL的业务逻辑是完全一致的！
-- 第二条SQL只是第一条SQL的重构版本，使用了嵌套CASE来实现相同的逻辑

-- ========== 性能差异分析 ==========
-- 第一条SQL：
-- - 优点：逻辑清晰，条件判断直接
-- - 缺点：重复判断基础条件，可能效率稍低

-- 第二条SQL：
-- - 优点：避免重复判断基础条件
-- - 缺点：嵌套CASE可读性稍差

-- ========== 推荐的统一优化版本 ==========
SELECT t4.appointmentdealno as preId,
    CASE 
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
            CASE 
                WHEN (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
                    CASE 
                        WHEN t1.nav > 0 THEN t1.nav
                        ELSE (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
                    END
                ELSE 0
            END
        ELSE navt.nav
    END as nav
FROM ods.raw_cm_zt_orderinfo t4
INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON t3.fund_code = navt.fund_code 
    AND t3.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                              '3286078','3355240','3352174','3286123','3341731');
