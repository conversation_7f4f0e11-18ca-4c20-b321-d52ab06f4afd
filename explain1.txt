Gather Motion 72:1  (slice4; segments: 72)  (cost=0.00..1013.01 rows=11764 width=16) (actual time=613.079..1544.364 rows=10 loops=1)
  Output: raw_cm_zt_orderinfo.appointmentdealno, (CASE WHEN ((dw_trade_mid_ack_gd.ack_amt > 0::numeric) AND (dw_trade_mid_ack_gd.ack_vol > 0::numeric)) THEN CASE WHEN ((dw_trade_mid_ack_gd.ack_amt_rmb - COALESCE(dw_trade_mid_ack_gd.fee, 0::numeric)) > 0::numeric) THEN COALESCE(CASE WHEN (dw_trade_mid_ack_gd.nav > 0::numeric) THEN dw_trade_mid_ack_gd.nav ELSE NULL::numeric END, ((dw_trade_mid_ack_gd.ack_amt_rmb - COALESCE(dw_trade_mid_ack_gd.fee, 0::numeric)) / dw_trade_mid_ack_gd.ack_vol)) ELSE 0::numeric END ELSE dw_pd_fund_nav.nav END)
  ->  Result  (cost=0.00..1012.58 rows=164 width=16) (actual time=576.281..1542.971 rows=3 loops=1)
        Output: raw_cm_zt_orderinfo.appointmentdealno, CASE WHEN ((dw_trade_mid_ack_gd.ack_amt > 0::numeric) AND (dw_trade_mid_ack_gd.ack_vol > 0::numeric)) THEN CASE WHEN ((dw_trade_mid_ack_gd.ack_amt_rmb - COALESCE(dw_trade_mid_ack_gd.fee, 0::numeric)) > 0::numeric) THEN COALESCE(CASE WHEN (dw_trade_mid_ack_gd.nav > 0::numeric) THEN dw_trade_mid_ack_gd.nav ELSE NULL::numeric END, ((dw_trade_mid_ack_gd.ack_amt_rmb - COALESCE(dw_trade_mid_ack_gd.fee, 0::numeric)) / dw_trade_mid_ack_gd.ack_vol)) ELSE 0::numeric END ELSE dw_pd_fund_nav.nav END
        ->  Nested Loop Left Join  (cost=0.00..1012.58 rows=164 width=39) (actual time=576.197..1542.850 rows=3 loops=1)
              Output: raw_cm_zt_orderinfo.appointmentdealno, dw_trade_mid_ack_gd.nav, dw_trade_mid_ack_gd.ack_amt, dw_trade_mid_ack_gd.ack_vol, dw_trade_mid_ack_gd.ack_amt_rmb, dw_trade_mid_ack_gd.fee, dw_pd_fund_nav.nav
              Join Filter: true
              ->  Redistribute Motion 72:72  (slice3; segments: 72)  (cost=0.00..939.10 rows=1 width=50) (actual time=91.446..91.606 rows=3 loops=1)
                    Output: raw_high_deal_order_dtl_high.fund_code, raw_high_deal_order_dtl_high.ack_dt, raw_cm_zt_orderinfo.appointmentdealno, dw_trade_mid_ack_gd.nav, dw_trade_mid_ack_gd.ack_amt, dw_trade_mid_ack_gd.ack_vol, dw_trade_mid_ack_gd.ack_amt_rmb, dw_trade_mid_ack_gd.fee
                    Hash Key: raw_high_deal_order_dtl_high.fund_code
                    ->  Nested Loop  (cost=0.00..939.10 rows=1 width=50) (actual time=50.732..89.294 rows=2 loops=1)
                          Output: raw_high_deal_order_dtl_high.fund_code, raw_high_deal_order_dtl_high.ack_dt, raw_cm_zt_orderinfo.appointmentdealno, dw_trade_mid_ack_gd.nav, dw_trade_mid_ack_gd.ack_amt, dw_trade_mid_ack_gd.ack_vol, dw_trade_mid_ack_gd.ack_amt_rmb, dw_trade_mid_ack_gd.fee
                          Join Filter: true
                          ->  Redistribute Motion 72:72  (slice2; segments: 72)  (cost=0.00..865.94 rows=1 width=48) (actual time=50.317..88.733 rows=2 loops=1)
                                Output: raw_high_deal_order_dtl_high.deal_dtl_no, raw_high_deal_order_dtl_high.fund_code, raw_high_deal_order_dtl_high.ack_dt, raw_cm_zt_orderinfo.appointmentdealno
                                Hash Key: raw_high_deal_order_dtl_high.deal_dtl_no
                                ->  Hash Join  (cost=0.00..865.94 rows=1 width=48) (actual time=50.181..72.071 rows=1 loops=1)
                                      Output: raw_high_deal_order_dtl_high.deal_dtl_no, raw_high_deal_order_dtl_high.fund_code, raw_high_deal_order_dtl_high.ack_dt, raw_cm_zt_orderinfo.appointmentdealno
                                      Hash Cond: ((raw_high_deal_order_dtl_high.deal_no)::text = (raw_cm_zt_orderinfo.dealno)::text)
                                      Executor Memory: 45kB  Segments: 72  Max: 1kB (segment 0)
                                      work_mem: 45kB  Segments: 72  Max: 1kB (segment 0)  Workfile: (0 spilling)
                                      Extra Text: (seg0)   Hash chain length 1.0 avg, 1 max, using 10 of 4194304 buckets.
                                      ->  Seq Scan on ods.raw_high_deal_order_dtl_high  (cost=0.00..432.81 rows=4606 width=66) (actual time=0.065..12.325 rows=4739 loops=1)
                                            Output: raw_high_deal_order_dtl_high.deal_dtl_no, raw_high_deal_order_dtl_high.deal_no, raw_high_deal_order_dtl_high.fund_code, raw_high_deal_order_dtl_high.ack_dt
                                      ->  Hash  (cost=431.57..431.57 rows=13 width=32) (actual time=0.090..0.090 rows=10 loops=1)
                                            Output: raw_cm_zt_orderinfo.dealno, raw_cm_zt_orderinfo.appointmentdealno
                                            ->  Broadcast Motion 72:72  (slice1; segments: 72)  (cost=0.00..431.57 rows=13 width=32) (actual time=0.026..0.073 rows=10 loops=1)
                                                  Output: raw_cm_zt_orderinfo.dealno, raw_cm_zt_orderinfo.appointmentdealno
                                                  ->  Seq Scan on ods.raw_cm_zt_orderinfo  (cost=0.00..431.57 rows=1 width=32) (actual time=8.795..9.011 rows=1 loops=1)
                                                        Output: raw_cm_zt_orderinfo.dealno, raw_cm_zt_orderinfo.appointmentdealno
                                                        Filter: ((raw_cm_zt_orderinfo.appointmentdealno)::text = ANY ('{3338098,3332238,3296540,3362156,3332260,3286078,3355240,3352174,3286123,3341731}'::text[]))
                          ->  Index Scan using pk_trade_mid_ack_gd on dw.dw_trade_mid_ack_gd  (cost=0.00..73.16 rows=1 width=27) (actual time=0.188..0.252 rows=1 loops=2)
                                Output: dw_trade_mid_ack_gd.nav, dw_trade_mid_ack_gd.ack_amt, dw_trade_mid_ack_gd.ack_vol, dw_trade_mid_ack_gd.ack_amt_rmb, dw_trade_mid_ack_gd.fee
                                Index Cond: ((dw_trade_mid_ack_gd.deal_dtl_no)::text = (raw_high_deal_order_dtl_high.deal_dtl_no)::text)
              ->  Sequence  (cost=0.00..73.46 rows=1 width=5) (actual time=161.579..483.733 rows=1 loops=3)
                    Output: dw_pd_fund_nav.trade_dt, dw_pd_fund_nav.fund_code, dw_pd_fund_nav.nav
                    ->  Partition Selector for dw_pd_fund_nav (dynamic scan id: 1)  (cost=10.00..100.00 rows=2 width=4) (never executed)
                          Partitions selected: 143 (out of 143)
                    ->  Dynamic Index Scan on dw.dw_pd_fund_nav (dynamic scan id: 1)  (cost=0.00..73.46 rows=1 width=5) (actual time=161.562..483.700 rows=1 loops=3)
                          Output: dw_pd_fund_nav.trade_dt, dw_pd_fund_nav.fund_code, dw_pd_fund_nav.nav
                          Index Cond: ((dw_pd_fund_nav.fund_code)::text = (raw_high_deal_order_dtl_high.fund_code)::text)
                          Filter: (((dw_pd_fund_nav.fund_code)::text = (raw_high_deal_order_dtl_high.fund_code)::text) AND ((raw_high_deal_order_dtl_high.ack_dt)::text = to_char((dw_pd_fund_nav.trade_dt)::timestamp with time zone, 'yyyymmdd'::text)))
                          Partitions scanned:  Avg 68.1 (out of 143) x 7 workers of 3 scans.  Max 143 parts (seg71).
Planning time: 361.603 ms
  (slice0)    Executor memory: 333K bytes.
  (slice1)    Executor memory: 62K bytes avg x 72 workers, 75K bytes max (seg8).
  (slice2)    Executor memory: 32857K bytes avg x 72 workers, 32864K bytes max (seg0).  Work_mem: 1K bytes max.
  (slice3)    Executor memory: 105K bytes avg x 72 workers, 112K bytes max (seg17).
  (slice4)    Executor memory: 371K bytes avg x 72 workers, 4192K bytes max (seg71).
  (slice5)    
  (slice6)    
  (slice7)    
Memory used:  1376256kB
Optimizer: Pivotal Optimizer (GPORCA)
Execution time: 1629.125 ms