select apply_appserialno as preId,
    CASE
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 AND t1.nav > 0 THEN
            t1.nav
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
            (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
            0
        ELSE
            COALESCE(navt.nav, 0)
    END as nav
from ods.raw_cm_custtrade_direct_high t2
inner join dw.dw_trade_mid_ack_gd t1 on appserialno = t1.deal_dtl_no
left join dw.dw_pd_fund_nav navt on t2.fundcode = navt.fund_code and t2.ack_dt = to_char(navt.trade_dt, 'yyyymmdd')
where
--直销
t2.apply_appserialno in ('71418','71815','71815','73828','73925','69822','62608','60755','74171','73789');


-- ========== 基于执行计划的优化分析 ==========
-- 执行时间：2567ms，返回9行数据，内存1.3GB
-- 关键发现：LEFT JOIN dw_pd_fund_nav 实际从未执行 (never executed)
-- 但分区选择器仍准备了143个分区，造成不必要的资源浪费

-- ========== 优化方案一：简化版本（推荐，如果确定不需要nav表） ==========
SELECT
    t2.apply_appserialno as preId,
    CASE
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 AND t1.nav > 0 THEN
            t1.nav
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
            (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
            0
        ELSE
            0  -- 直接返回0，避免LEFT JOIN开销
    END as nav
FROM ods.raw_cm_custtrade_direct_high t2
INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t2.appserialno = t1.deal_dtl_no
WHERE t2.apply_appserialno IN ('71418','71815','71815','73828','73925',
                               '69822','62608','60755','74171','73789');


-- ========== 优化方案二：分离处理策略 ==========
WITH base_data AS (
    SELECT
        t2.apply_appserialno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t2.fundcode,
        t2.ack_dt,
        -- 预计算条件，避免重复计算
        (t1.ack_amt > 0 AND t1.ack_vol > 0) as has_valid_amounts,
        ((t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0) as has_positive_net,
        (t1.nav > 0) as has_positive_nav
    FROM ods.raw_cm_custtrade_direct_high t2
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t2.appserialno = t1.deal_dtl_no
    WHERE t2.apply_appserialno IN ('71418','71815','71815','73828','73925',
                                   '69822','62608','60755','74171','73789')
)
-- 不需要nav表的记录（大部分情况）
SELECT
    preId,
    CASE
        WHEN has_valid_amounts AND has_positive_net AND has_positive_nav THEN nav
        WHEN has_valid_amounts AND has_positive_net THEN (ack_amt_rmb - COALESCE(fee, 0)) / ack_vol
        WHEN has_valid_amounts THEN 0
    END as nav
FROM base_data
WHERE has_valid_amounts

UNION ALL

-- 需要nav表的记录（少数情况）
SELECT
    bd.preId,
    COALESCE(navt.nav, 0) as nav
FROM base_data bd
LEFT JOIN dw.dw_pd_fund_nav navt
    ON bd.fundcode = navt.fund_code
    AND bd.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
WHERE NOT bd.has_valid_amounts;


-- ========== 优化方案三：CTE优化版本 ==========
WITH filtered_data AS (
    -- 先过滤出需要的记录
    SELECT
        t2.apply_appserialno as preId,
        t2.appserialno,
        t2.fundcode,
        t2.ack_dt
    FROM ods.raw_cm_custtrade_direct_high t2
    WHERE t2.apply_appserialno IN ('71418','71815','71815','73828','73925',
                                   '69822','62608','60755','74171','73789')
),
main_data AS (
    -- 完成主要JOIN，获取交易数据
    SELECT
        fd.preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        fd.fundcode,
        fd.ack_dt
    FROM filtered_data fd
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON fd.appserialno = t1.deal_dtl_no
)
SELECT
    md.preId,
    CASE
        WHEN md.ack_amt > 0 AND md.ack_vol > 0 AND (md.ack_amt_rmb - COALESCE(md.fee, 0)) > 0 AND md.nav > 0 THEN
            md.nav
        WHEN md.ack_amt > 0 AND md.ack_vol > 0 AND (md.ack_amt_rmb - COALESCE(md.fee, 0)) > 0 THEN
            (md.ack_amt_rmb - COALESCE(md.fee, 0)) / md.ack_vol
        WHEN md.ack_amt > 0 AND md.ack_vol > 0 THEN
            0
        ELSE
            COALESCE(navt.nav, 0)
    END as nav
FROM main_data md
LEFT JOIN dw.dw_pd_fund_nav navt
    ON md.fundcode = navt.fund_code
    AND md.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');