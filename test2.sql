select apply_appserialno as preId,
    CASE
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 AND t1.nav > 0 THEN
            t1.nav
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
            (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
            0
        ELSE
            COALESCE(navt.nav, 0)
    END as nav
from ods.raw_cm_custtrade_direct_high t2
inner join dw.dw_trade_mid_ack_gd t1 on appserialno = t1.deal_dtl_no
left join dw.dw_pd_fund_nav navt on t2.fundcode = navt.fund_code and t2.ack_dt = to_char(navt.trade_dt, 'yyyymmdd')
where
--直销
t2.apply_appserialno in ('71418','71815','71815','73828','73925','69822','62608','60755','74171','73789');


-- ========== 保留nav表的优化版本（推荐使用） ==========
-- 既然nav表不能去掉，优化策略：减少分区扫描开销，优化JOIN效率

-- 方案A：智能分离处理（最推荐）
-- 只对真正需要nav表的记录进行LEFT JOIN
WITH base_data AS (
    SELECT
        t2.apply_appserialno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t2.fundcode,
        t2.ack_dt,
        -- 判断是否需要查询nav表
        CASE
            WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN 0
            ELSE 1
        END as need_nav_lookup
    FROM ods.raw_cm_custtrade_direct_high t2
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t2.appserialno = t1.deal_dtl_no
    WHERE t2.apply_appserialno IN ('71418','71815','71815','73828','73925',
                                   '69822','62608','60755','74171','73789')
),
-- 预先获取需要的nav数据，减少分区扫描
nav_data AS (
    SELECT DISTINCT
        bd.fundcode,
        bd.ack_dt,
        navt.nav
    FROM base_data bd
    INNER JOIN dw.dw_pd_fund_nav navt
        ON bd.fundcode = navt.fund_code
        AND bd.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
    WHERE bd.need_nav_lookup = 1  -- 只查询真正需要的
)
SELECT
    bd.preId,
    CASE
        WHEN bd.ack_amt > 0 AND bd.ack_vol > 0 AND (bd.ack_amt_rmb - COALESCE(bd.fee, 0)) > 0 AND bd.nav > 0 THEN
            bd.nav
        WHEN bd.ack_amt > 0 AND bd.ack_vol > 0 AND (bd.ack_amt_rmb - COALESCE(bd.fee, 0)) > 0 THEN
            (bd.ack_amt_rmb - COALESCE(bd.fee, 0)) / bd.ack_vol
        WHEN bd.ack_amt > 0 AND bd.ack_vol > 0 THEN
            0
        ELSE
            COALESCE(nd.nav, 0)
    END as nav
FROM base_data bd
LEFT JOIN nav_data nd ON bd.fundcode = nd.fundcode AND bd.ack_dt = nd.ack_dt;


-- 方案B：条件前置优化
WITH filtered_base AS (
    SELECT
        t2.apply_appserialno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t2.fundcode,
        t2.ack_dt,
        -- 预计算条件，避免重复计算
        (t1.ack_amt > 0 AND t1.ack_vol > 0) as has_valid_amounts,
        ((t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0) as has_positive_net,
        (t1.nav > 0) as has_positive_nav,
        (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) as net_amount
    FROM ods.raw_cm_custtrade_direct_high t2
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t2.appserialno = t1.deal_dtl_no
    WHERE t2.apply_appserialno IN ('71418','71815','71815','73828','73925',
                                   '69822','62608','60755','74171','73789')
)
SELECT
    fb.preId,
    CASE
        WHEN fb.has_valid_amounts AND fb.has_positive_net AND fb.has_positive_nav THEN
            fb.nav
        WHEN fb.has_valid_amounts AND fb.has_positive_net THEN
            fb.net_amount / fb.ack_vol
        WHEN fb.has_valid_amounts THEN
            0
        ELSE
            COALESCE(navt.nav, 0)
    END as nav
FROM filtered_base fb
LEFT JOIN dw.dw_pd_fund_nav navt
    ON fb.fundcode = navt.fund_code
    AND fb.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');


-- 方案C：最小改动版本（修复表别名）
SELECT
    t2.apply_appserialno as preId,
    CASE
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 AND t1.nav > 0 THEN
            t1.nav
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
            (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
            0
        ELSE
            COALESCE(navt.nav, 0)
    END as nav
FROM ods.raw_cm_custtrade_direct_high t2
INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t2.appserialno = t1.deal_dtl_no  -- 修复表别名
LEFT JOIN dw.dw_pd_fund_nav navt ON t2.fundcode = navt.fund_code AND t2.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
WHERE t2.apply_appserialno IN ('71418','71815','71815','73828','73925','69822','62608','60755','74171','73789');