select apply_appserialno as preId, 
    CASE
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 AND t1.nav > 0 THEN
            t1.nav
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
            (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
            0
        ELSE
            COALESCE(navt.nav, 0)
    END as nav
from ods.raw_cm_custtrade_direct_high t2
inner join dw.dw_trade_mid_ack_gd t1 on appserialno = t1.deal_dtl_no
left join dw.dw_pd_fund_nav navt on t2.fundcode = navt.fund_code and t2.ack_dt = to_char(navt.trade_dt, 'yyyymmdd')
where
--直销
t2.apply_appserialno in ('71418','71815','71815','73828','73925','69822','62608','60755','74171','73789');