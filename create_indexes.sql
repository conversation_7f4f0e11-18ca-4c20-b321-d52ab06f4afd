-- 为test1.sql优化创建的关键索引
-- 请在非生产环境先测试，确认性能提升后再在生产环境执行

-- 1. 最重要：为appointmentdealno创建索引
-- 这个索引将查询从全表扫描(20万条)变为索引查找(10条)
CREATE INDEX CONCURRENTLY idx_raw_cm_zt_orderinfo_appointmentdealno 
ON ods.raw_cm_zt_orderinfo(appointmentdealno);

-- 2. 为deal_no创建索引，优化第一个JOIN
CREATE INDEX CONCURRENTLY idx_raw_high_deal_order_dtl_high_deal_no 
ON ods.raw_high_deal_order_dtl_high(deal_no);

-- 3. 为LEFT JOIN创建复合索引
CREATE INDEX CONCURRENTLY idx_raw_high_deal_order_dtl_high_fund_ack 
ON ods.raw_high_deal_order_dtl_high(fund_code, ack_dt);

-- 4. 为dw_pd_fund_nav表创建复合索引（如果不存在）
CREATE INDEX CONCURRENTLY idx_dw_pd_fund_nav_fund_trade_dt 
ON dw.dw_pd_fund_nav(fund_code, trade_dt);

-- 注意：
-- 1. 使用CONCURRENTLY避免锁表
-- 2. 建议在业务低峰期执行
-- 3. 监控索引创建进度和系统资源使用情况
