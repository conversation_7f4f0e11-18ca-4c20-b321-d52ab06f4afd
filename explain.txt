Gather Motion 72:1  (slice3; segments: 72)  (cost=0.00..724.83 rows=23455 width=16) (actual time=724.177..2563.743 rows=9 loops=1)
  Output: raw_cm_custtrade_direct_high.apply_appserialno, (CASE WHEN ((dw_trade_mid_ack_gd.ack_amt > 0::numeric) AND (dw_trade_mid_ack_gd.ack_vol > 0::numeric) AND ((dw_trade_mid_ack_gd.ack_amt_rmb - COALESCE(dw_trade_mid_ack_gd.fee, 0::numeric)) > 0::numeric) AND (dw_trade_mid_ack_gd.nav > 0::numeric)) THEN dw_trade_mid_ack_gd.nav WHEN ((dw_trade_mid_ack_gd.ack_amt > 0::numeric) AND (dw_trade_mid_ack_gd.ack_vol > 0::numeric) AND ((dw_trade_mid_ack_gd.ack_amt_rmb - COALESCE(dw_trade_mid_ack_gd.fee, 0::numeric)) > 0::numeric)) THEN ((dw_trade_mid_ack_gd.ack_amt_rmb - COALESCE(dw_trade_mid_ack_gd.fee, 0::numeric)) / dw_trade_mid_ack_gd.ack_vol) WHEN ((dw_trade_mid_ack_gd.ack_amt > 0::numeric) AND (dw_trade_mid_ack_gd.ack_vol > 0::numeric)) THEN 0::numeric ELSE COALESCE(dw_pd_fund_nav.nav, 0::numeric) END)
  ->  Result  (cost=0.00..723.98 rows=326 width=16) (actual time=918.856..2562.645 rows=3 loops=1)
        Output: raw_cm_custtrade_direct_high.apply_appserialno, CASE WHEN ((dw_trade_mid_ack_gd.ack_amt > 0::numeric) AND (dw_trade_mid_ack_gd.ack_vol > 0::numeric) AND ((dw_trade_mid_ack_gd.ack_amt_rmb - COALESCE(dw_trade_mid_ack_gd.fee, 0::numeric)) > 0::numeric) AND (dw_trade_mid_ack_gd.nav > 0::numeric)) THEN dw_trade_mid_ack_gd.nav WHEN ((dw_trade_mid_ack_gd.ack_amt > 0::numeric) AND (dw_trade_mid_ack_gd.ack_vol > 0::numeric) AND ((dw_trade_mid_ack_gd.ack_amt_rmb - COALESCE(dw_trade_mid_ack_gd.fee, 0::numeric)) > 0::numeric)) THEN ((dw_trade_mid_ack_gd.ack_amt_rmb - COALESCE(dw_trade_mid_ack_gd.fee, 0::numeric)) / dw_trade_mid_ack_gd.ack_vol) WHEN ((dw_trade_mid_ack_gd.ack_amt > 0::numeric) AND (dw_trade_mid_ack_gd.ack_vol > 0::numeric)) THEN 0::numeric ELSE COALESCE(dw_pd_fund_nav.nav, 0::numeric) END
        ->  Nested Loop Left Join  (cost=0.00..723.98 rows=326 width=38) (actual time=918.845..2562.615 rows=3 loops=1)
              Output: raw_cm_custtrade_direct_high.apply_appserialno, dw_trade_mid_ack_gd.nav, dw_trade_mid_ack_gd.ack_amt, dw_trade_mid_ack_gd.ack_vol, dw_trade_mid_ack_gd.ack_amt_rmb, dw_trade_mid_ack_gd.fee, dw_pd_fund_nav.nav
              Join Filter: true
              ->  Redistribute Motion 72:72  (slice2; segments: 72)  (cost=0.00..577.27 rows=1 width=49) (actual time=6.568..6.801 rows=3 loops=1)
                    Output: raw_cm_custtrade_direct_high.fundcode, raw_cm_custtrade_direct_high.apply_appserialno, raw_cm_custtrade_direct_high.ack_dt, dw_trade_mid_ack_gd.nav, dw_trade_mid_ack_gd.ack_amt, dw_trade_mid_ack_gd.ack_vol, dw_trade_mid_ack_gd.ack_amt_rmb, dw_trade_mid_ack_gd.fee
                    Hash Key: raw_cm_custtrade_direct_high.fundcode
                    ->  Nested Loop  (cost=0.00..577.27 rows=1 width=49) (actual time=1.561..4.361 rows=1 loops=1)
                          Output: raw_cm_custtrade_direct_high.fundcode, raw_cm_custtrade_direct_high.apply_appserialno, raw_cm_custtrade_direct_high.ack_dt, dw_trade_mid_ack_gd.nav, dw_trade_mid_ack_gd.ack_amt, dw_trade_mid_ack_gd.ack_vol, dw_trade_mid_ack_gd.ack_amt_rmb, dw_trade_mid_ack_gd.fee
                          Join Filter: true
                          ->  Redistribute Motion 72:72  (slice1; segments: 72)  (cost=0.00..431.21 rows=1 width=31) (actual time=1.300..4.098 rows=1 loops=1)
                                Output: raw_cm_custtrade_direct_high.appserialno, raw_cm_custtrade_direct_high.fundcode, raw_cm_custtrade_direct_high.apply_appserialno, raw_cm_custtrade_direct_high.ack_dt
                                Hash Key: raw_cm_custtrade_direct_high.appserialno
                                ->  Seq Scan on ods.raw_cm_custtrade_direct_high  (cost=0.00..431.21 rows=1 width=31) (actual time=0.068..1.029 rows=4 loops=1)
                                      Output: raw_cm_custtrade_direct_high.appserialno, raw_cm_custtrade_direct_high.fundcode, raw_cm_custtrade_direct_high.apply_appserialno, raw_cm_custtrade_direct_high.ack_dt
                                      Filter: ((raw_cm_custtrade_direct_high.apply_appserialno)::text = ANY ('{71418,71815,71815,73828,73925,69822,62608,60755,74171,73789}'::text[]))
                          ->  Index Scan using pk_trade_mid_ack_gd on dw.dw_trade_mid_ack_gd  (cost=0.00..146.06 rows=1 width=27) (actual time=0.241..0.242 rows=1 loops=1)
                                Output: dw_trade_mid_ack_gd.nav, dw_trade_mid_ack_gd.ack_amt, dw_trade_mid_ack_gd.ack_vol, dw_trade_mid_ack_gd.ack_amt_rmb, dw_trade_mid_ack_gd.fee
                                Index Cond: ((dw_trade_mid_ack_gd.deal_dtl_no)::text = (raw_cm_custtrade_direct_high.appserialno)::text)
              ->  Sequence  (cost=0.00..146.67 rows=1 width=5) (never executed)
                    Output: dw_pd_fund_nav.trade_dt, dw_pd_fund_nav.fund_code, dw_pd_fund_nav.nav
                    ->  Partition Selector for dw_pd_fund_nav (dynamic scan id: 1)  (cost=10.00..100.00 rows=2 width=4) (never executed)
                          Partitions selected: 143 (out of 143)
                    ->  Dynamic Index Scan on dw.dw_pd_fund_nav (dynamic scan id: 1)  (cost=0.00..146.67 rows=1 width=5) (never executed)
                          Output: dw_pd_fund_nav.trade_dt, dw_pd_fund_nav.fund_code, dw_pd_fund_nav.nav
                          Index Cond: ((dw_pd_fund_nav.fund_code)::text = (raw_cm_custtrade_direct_high.fundcode)::text)
                          Filter: (((dw_pd_fund_nav.fund_code)::text = (raw_cm_custtrade_direct_high.fundcode)::text) AND ((raw_cm_custtrade_direct_high.ack_dt)::text = to_char((dw_pd_fund_nav.trade_dt)::timestamp with time zone, 'yyyymmdd'::text)))
                          Partitions scanned:  Avg 183.9 (out of 143) x 7 workers.  Max 429 parts (seg69).
Planning time: 246.645 ms
  (slice0)    Executor memory: 321K bytes.
  (slice1)    Executor memory: 63K bytes avg x 72 workers, 63K bytes max (seg0).
  (slice2)    Executor memory: 105K bytes avg x 72 workers, 112K bytes max (seg3).
  (slice3)    Executor memory: 447K bytes avg x 72 workers, 7552K bytes max (seg69).
  (slice4)    
  (slice5)    
  (slice6)    
Memory used:  1376256kB
Optimizer: Pivotal Optimizer (GPORCA)
Execution time: 2567.810 ms