Gather Motion 72:1  (slice5; segments: 72)  (cost=0.00..2190.56 rows=10 width=16) (actual time=1615.206..1616.042 rows=10 loops=1)
  Output: share0_ref3.appointmentdealno, (CASE WHEN ((share0_ref3.ack_amt > 0::numeric) AND (share0_ref3.ack_vol > 0::numeric) AND ((share0_ref3.ack_amt_rmb - COALESCE(share0_ref3.fee, 0::numeric)) > 0::numeric) AND (share0_ref3.nav > 0::numeric)) THEN share0_ref3.nav WHEN ((share0_ref3.ack_amt > 0::numeric) AND (share0_ref3.ack_vol > 0::numeric) AND ((share0_ref3.ack_amt_rmb - COALESCE(share0_ref3.fee, 0::numeric)) > 0::numeric)) THEN ((share0_ref3.ack_amt_rmb - COALESCE(share0_ref3.fee, 0::numeric)) / share0_ref3.ack_vol) WHEN ((share0_ref3.ack_amt > 0::numeric) AND (share0_ref3.ack_vol > 0::numeric)) THEN 0::numeric ELSE COALESCE(dw_pd_fund_nav.nav, 0::numeric) END)
  ->  Sequence  (cost=0.00..2190.56 rows=1 width=16) (actual time=1610.420..1612.116 rows=2 loops=1)
        Output: share0_ref3.appointmentdealno, (CASE WHEN ((share0_ref3.ack_amt > 0::numeric) AND (share0_ref3.ack_vol > 0::numeric) AND ((share0_ref3.ack_amt_rmb - COALESCE(share0_ref3.fee, 0::numeric)) > 0::numeric) AND (share0_ref3.nav > 0::numeric)) THEN share0_ref3.nav WHEN ((share0_ref3.ack_amt > 0::numeric) AND (share0_ref3.ack_vol > 0::numeric) AND ((share0_ref3.ack_amt_rmb - COALESCE(share0_ref3.fee, 0::numeric)) > 0::numeric)) THEN ((share0_ref3.ack_amt_rmb - COALESCE(share0_ref3.fee, 0::numeric)) / share0_ref3.ack_vol) WHEN ((share0_ref3.ack_amt > 0::numeric) AND (share0_ref3.ack_vol > 0::numeric)) THEN 0::numeric ELSE COALESCE(dw_pd_fund_nav.nav, 0::numeric) END)
        ->  Shared Scan (share slice:id 5:0)  (cost=0.00..1299.16 rows=1 width=1) (actual time=51.313..51.324 rows=2 loops=1)
              Output: share0_ref1.appointmentdealno, share0_ref1.ack_amt, share0_ref1.ack_vol, share0_ref1.ack_amt_rmb, share0_ref1.fee, share0_ref1.nav, share0_ref1.fund_code, share0_ref1.ack_dt
              ->  Materialize  (cost=0.00..1299.16 rows=1 width=1) (never executed)
                    Output: raw_cm_zt_orderinfo.appointmentdealno, dw_trade_mid_ack_gd.ack_amt, dw_trade_mid_ack_gd.ack_vol, dw_trade_mid_ack_gd.ack_amt_rmb, dw_trade_mid_ack_gd.fee, dw_trade_mid_ack_gd.nav, raw_high_deal_order_dtl_high.fund_code, raw_high_deal_order_dtl_high.ack_dt
                    ->  Hash Join  (cost=0.00..1299.16 rows=1 width=50) (actual time=42.698..50.811 rows=2 loops=1)
                          Output: raw_cm_zt_orderinfo.appointmentdealno, dw_trade_mid_ack_gd.ack_amt, dw_trade_mid_ack_gd.ack_vol, dw_trade_mid_ack_gd.ack_amt_rmb, dw_trade_mid_ack_gd.fee, dw_trade_mid_ack_gd.nav, raw_high_deal_order_dtl_high.fund_code, raw_high_deal_order_dtl_high.ack_dt
                          Hash Cond: ((dw_trade_mid_ack_gd.deal_dtl_no)::text = (raw_high_deal_order_dtl_high.deal_dtl_no)::text)
                          Executor Memory: 57kB  Segments: 72  Max: 1kB (segment 0)
                          work_mem: 57kB  Segments: 72  Max: 1kB (segment 0)  Workfile: (0 spilling)
                          Extra Text: (seg53)  Hash chain length 1.0 avg, 1 max, using 10 of 524288 buckets.
                          ->  Seq Scan on dw.dw_trade_mid_ack_gd  (cost=0.00..431.80 rows=4738 width=50) (actual time=0.039..3.448 rows=4887 loops=1)
                                Output: dw_trade_mid_ack_gd.deal_dtl_no, dw_trade_mid_ack_gd.nav, dw_trade_mid_ack_gd.ack_amt, dw_trade_mid_ack_gd.ack_vol, dw_trade_mid_ack_gd.ack_amt_rmb, dw_trade_mid_ack_gd.fee
                          ->  Hash  (cost=865.94..865.94 rows=13 width=48) (actual time=27.549..27.549 rows=10 loops=1)
                                Output: raw_cm_zt_orderinfo.appointmentdealno, raw_high_deal_order_dtl_high.deal_dtl_no, raw_high_deal_order_dtl_high.fund_code, raw_high_deal_order_dtl_high.ack_dt
                                ->  Broadcast Motion 72:72  (slice4; segments: 72)  (cost=0.00..865.94 rows=13 width=48) (actual time=4.624..27.518 rows=10 loops=1)
                                      Output: raw_cm_zt_orderinfo.appointmentdealno, raw_high_deal_order_dtl_high.deal_dtl_no, raw_high_deal_order_dtl_high.fund_code, raw_high_deal_order_dtl_high.ack_dt
                                      ->  Hash Join  (cost=0.00..865.94 rows=1 width=48) (actual time=13.601..16.822 rows=1 loops=1)
                                            Output: raw_cm_zt_orderinfo.appointmentdealno, raw_high_deal_order_dtl_high.deal_dtl_no, raw_high_deal_order_dtl_high.fund_code, raw_high_deal_order_dtl_high.ack_dt
                                            Hash Cond: ((raw_high_deal_order_dtl_high.deal_no)::text = (raw_cm_zt_orderinfo.dealno)::text)
                                            Executor Memory: 45kB  Segments: 72  Max: 1kB (segment 0)
                                            work_mem: 45kB  Segments: 72  Max: 1kB (segment 0)  Workfile: (0 spilling)
                                            Extra Text: (seg0)   Hash chain length 1.0 avg, 1 max, using 10 of 2097152 buckets.
                                            ->  Seq Scan on ods.raw_high_deal_order_dtl_high  (cost=0.00..432.81 rows=4606 width=66) (actual time=0.020..4.827 rows=4739 loops=1)
                                                  Output: raw_high_deal_order_dtl_high.deal_dtl_no, raw_high_deal_order_dtl_high.deal_no, raw_high_deal_order_dtl_high.fund_code, raw_high_deal_order_dtl_high.ack_dt
                                            ->  Hash  (cost=431.57..431.57 rows=13 width=32) (actual time=0.086..0.086 rows=10 loops=1)
                                                  Output: raw_cm_zt_orderinfo.dealno, raw_cm_zt_orderinfo.appointmentdealno
                                                  ->  Broadcast Motion 72:72  (slice3; segments: 72)  (cost=0.00..431.57 rows=13 width=32) (actual time=0.024..0.079 rows=10 loops=1)
                                                        Output: raw_cm_zt_orderinfo.dealno, raw_cm_zt_orderinfo.appointmentdealno
                                                        ->  Seq Scan on ods.raw_cm_zt_orderinfo  (cost=0.00..431.57 rows=1 width=32) (actual time=1.302..1.382 rows=1 loops=1)
                                                              Output: raw_cm_zt_orderinfo.dealno, raw_cm_zt_orderinfo.appointmentdealno
                                                              Filter: ((raw_cm_zt_orderinfo.appointmentdealno)::text = ANY ('{3338098,3332238,3296540,3362156,3332260,3286078,3355240,3352174,3286123,3341731}'::text[]))
        ->  Result  (cost=0.00..891.39 rows=1 width=16) (actual time=1559.094..1560.790 rows=2 loops=1)
              Output: share0_ref3.appointmentdealno, CASE WHEN ((share0_ref3.ack_amt > 0::numeric) AND (share0_ref3.ack_vol > 0::numeric) AND ((share0_ref3.ack_amt_rmb - COALESCE(share0_ref3.fee, 0::numeric)) > 0::numeric) AND (share0_ref3.nav > 0::numeric)) THEN share0_ref3.nav WHEN ((share0_ref3.ack_amt > 0::numeric) AND (share0_ref3.ack_vol > 0::numeric) AND ((share0_ref3.ack_amt_rmb - COALESCE(share0_ref3.fee, 0::numeric)) > 0::numeric)) THEN ((share0_ref3.ack_amt_rmb - COALESCE(share0_ref3.fee, 0::numeric)) / share0_ref3.ack_vol) WHEN ((share0_ref3.ack_amt > 0::numeric) AND (share0_ref3.ack_vol > 0::numeric)) THEN 0::numeric ELSE COALESCE(dw_pd_fund_nav.nav, 0::numeric) END
              ->  Hash Left Join  (cost=0.00..891.39 rows=1 width=39) (actual time=1558.961..1560.651 rows=2 loops=1)
                    Output: share0_ref3.appointmentdealno, share0_ref3.ack_amt, share0_ref3.ack_vol, share0_ref3.ack_amt_rmb, share0_ref3.fee, share0_ref3.nav, dw_pd_fund_nav.nav
                    Hash Cond: (((share0_ref3.fund_code)::text = (share0_ref2.fund_code)::text) AND ((share0_ref3.ack_dt)::text = (share0_ref2.ack_dt)::text))
                    Executor Memory: 31kB  Segments: 72  Max: 1kB (segment 0)
                    work_mem: 31kB  Segments: 72  Max: 1kB (segment 0)  Workfile: (0 spilling)
                    Extra Text: (seg53)  Hash chain length 1.0 avg, 1 max, using 8 of 524288 buckets.
                    ->  Shared Scan (share slice:id 5:0)  (cost=0.00..431.00 rows=1 width=50) (actual time=0.109..0.115 rows=2 loops=1)
                          Output: share0_ref3.appointmentdealno, share0_ref3.ack_amt, share0_ref3.ack_vol, share0_ref3.ack_amt_rmb, share0_ref3.fee, share0_ref3.nav, share0_ref3.fund_code, share0_ref3.ack_dt
                    ->  Hash  (cost=460.39..460.39 rows=6 width=21) (actual time=1560.336..1560.336 rows=8 loops=1)
                          Output: share0_ref2.fund_code, share0_ref2.ack_dt, dw_pd_fund_nav.nav
                          ->  Broadcast Motion 72:72  (slice2; segments: 72)  (cost=0.00..460.39 rows=6 width=21) (actual time=577.278..1560.305 rows=8 loops=1)
                                Output: share0_ref2.fund_code, share0_ref2.ack_dt, dw_pd_fund_nav.nav
                                ->  GroupAggregate  (cost=0.00..460.38 rows=1 width=21) (actual time=1610.466..1610.528 rows=2 loops=1)
                                      Output: share0_ref2.fund_code, share0_ref2.ack_dt, dw_pd_fund_nav.nav
                                      Group Key: share0_ref2.fund_code, share0_ref2.ack_dt, dw_pd_fund_nav.nav
                                      ->  Sort  (cost=0.00..460.38 rows=1 width=21) (actual time=1610.460..1610.521 rows=3 loops=1)
                                            Output: share0_ref2.fund_code, share0_ref2.ack_dt, dw_pd_fund_nav.nav
                                            Sort Key: share0_ref2.fund_code, share0_ref2.ack_dt, dw_pd_fund_nav.nav
                                            Sort Method:  quicksort  Memory: 2376kB  Max Memory: 33kB  Avg Memory: 33kb (72 segments)
                                            Executor Memory: 2308kB  Segments: 72  Max: 33kB (segment 0)
                                            work_mem: 2308kB  Segments: 72  Max: 33kB (segment 0)  Workfile: (0 spilling)
                                            ->  Nested Loop  (cost=0.00..460.38 rows=1 width=21) (actual time=521.338..1610.422 rows=3 loops=1)
                                                  Output: share0_ref2.fund_code, share0_ref2.ack_dt, dw_pd_fund_nav.nav
                                                  Join Filter: true
                                                  ->  Redistribute Motion 72:72  (slice1; segments: 72)  (cost=0.00..431.00 rows=1 width=16) (actual time=45.624..45.699 rows=3 loops=1)
                                                        Output: share0_ref2.fund_code, share0_ref2.ack_dt
                                                        Hash Key: share0_ref2.fund_code
                                                        ->  Result  (cost=0.00..431.00 rows=1 width=16) (actual time=0.074..0.082 rows=2 loops=1)
                                                              Output: share0_ref2.fund_code, share0_ref2.ack_dt
                                                              ->  Shared Scan (share slice:id 1:0)  (cost=0.00..431.00 rows=1 width=16) (actual time=0.067..0.073 rows=2 loops=1)
                                                                    Output: share0_ref2.appointmentdealno, share0_ref2.ack_amt, share0_ref2.ack_vol, share0_ref2.ack_amt_rmb, share0_ref2.fee, share0_ref2.nav, share0_ref2.fund_code, share0_ref2.ack_dt
                                                  ->  Sequence  (cost=0.00..29.38 rows=1 width=5) (actual time=158.569..521.564 rows=1 loops=3)
                                                        Output: dw_pd_fund_nav.nav, dw_pd_fund_nav.trade_dt, dw_pd_fund_nav.fund_code
                                                        ->  Partition Selector for dw_pd_fund_nav (dynamic scan id: 1)  (cost=10.00..100.00 rows=2 width=4) (never executed)
                                                              Partitions selected: 143 (out of 143)
                                                        ->  Dynamic Index Scan on dw.dw_pd_fund_nav (dynamic scan id: 1)  (cost=0.00..29.38 rows=1 width=5) (actual time=158.558..521.543 rows=1 loops=3)
                                                              Output: dw_pd_fund_nav.nav, dw_pd_fund_nav.trade_dt, dw_pd_fund_nav.fund_code
                                                              Index Cond: ((dw_pd_fund_nav.fund_code)::text = (share0_ref2.fund_code)::text)
                                                              Filter: (((dw_pd_fund_nav.fund_code)::text = (share0_ref2.fund_code)::text) AND ((share0_ref2.ack_dt)::text = to_char((dw_pd_fund_nav.trade_dt)::timestamp with time zone, 'YYYYMMDD'::text)))
                                                              Partitions scanned:  Avg 68.1 (out of 143) x 7 workers of 3 scans.  Max 143 parts (seg71).
Planning time: 340.780 ms
  (slice0)    Executor memory: 565K bytes.
  (slice1)    Executor memory: 156K bytes avg x 72 workers, 156K bytes max (seg0).
  (slice2)    Executor memory: 344K bytes avg x 72 workers, 4216K bytes max (seg71).  Work_mem: 33K bytes max.
  (slice3)    Executor memory: 62K bytes avg x 72 workers, 75K bytes max (seg8).
  (slice4)    Executor memory: 16475K bytes avg x 72 workers, 16496K bytes max (seg0).  Work_mem: 1K bytes max.
  (slice5)    Executor memory: 8731K bytes avg x 72 workers, 8752K bytes max (seg17).  Work_mem: 1K bytes max.
  (slice6)    
  (slice7)    
Memory used:  1376256kB
Optimizer: Pivotal Optimizer (GPORCA)
Execution time: 1621.245 ms