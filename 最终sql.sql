--直销
(WITH base_data AS (
    SELECT
        t2.apply_appserialno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t2.fundcode,
        t2.ack_dt,
        -- 判断是否需要查询nav表
        CASE
            WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN 0
            ELSE 1
        END as need_nav_lookup
    FROM ods.raw_cm_custtrade_direct_high t2
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t2.appserialno = t1.deal_dtl_no
    WHERE t2.apply_appserialno IN ('71418','71815','71815','73828','73925',
                                   '69822','62608','60755','74171','73789')
),
-- 预先获取需要的nav数据，减少分区扫描
nav_data AS (
    SELECT DISTINCT
        bd.fundcode,
        bd.ack_dt,
        navt.nav
    FROM base_data bd
    INNER JOIN dw.dw_pd_fund_nav navt
        ON bd.fundcode = navt.fund_code
        AND bd.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
    WHERE bd.need_nav_lookup = 1  -- 只查询真正需要的
)
SELECT
    bd.preId,
    CASE
        WHEN bd.ack_amt > 0 AND bd.ack_vol > 0 AND (bd.ack_amt_rmb - COALESCE(bd.fee, 0)) > 0 AND bd.nav > 0 THEN
            bd.nav
        WHEN bd.ack_amt > 0 AND bd.ack_vol > 0 AND (bd.ack_amt_rmb - COALESCE(bd.fee, 0)) > 0 THEN
            (bd.ack_amt_rmb - COALESCE(bd.fee, 0)) / bd.ack_vol
        WHEN bd.ack_amt > 0 AND bd.ack_vol > 0 THEN
            0
        ELSE
            nd.nav
    END as nav
FROM base_data bd
LEFT JOIN nav_data nd ON bd.fundcode = nd.fundcode AND bd.ack_dt = nd.ack_dt
)
union
--代销
(WITH base_data AS (
    SELECT
        t4.appointmentdealno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t3.fund_code fundcode,
        t3.ack_dt,
        -- 判断是否需要查询nav表
        CASE
            WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN 0
            ELSE 1
        END as need_nav_lookup
    FROM ods.raw_cm_zt_orderinfo t4
	inner join ods.raw_high_deal_order_dtl_high t3 on t3.deal_no  = t4.dealno
	inner join dw.dw_trade_mid_ack_gd t1 on t3.deal_dtl_no = t1.deal_dtl_no
    WHERE t4.appointmentdealno in ('3338098','3332238','3296540','3362156','3332260','3286078','3355240','3352174','3286123','3341731')
),
-- 预先获取需要的nav数据，减少分区扫描
nav_data AS (
    SELECT DISTINCT
        bd.fundcode,
        bd.ack_dt,
        navt.nav
    FROM base_data bd
    INNER JOIN dw.dw_pd_fund_nav navt
        ON bd.fundcode = navt.fund_code
        AND bd.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
    WHERE bd.need_nav_lookup = 1  -- 只查询真正需要的
)
SELECT
    bd.preId,
    CASE
        WHEN bd.ack_amt > 0 AND bd.ack_vol > 0 AND (bd.ack_amt_rmb - COALESCE(bd.fee, 0)) > 0 AND bd.nav > 0 THEN
            bd.nav
        WHEN bd.ack_amt > 0 AND bd.ack_vol > 0 AND (bd.ack_amt_rmb - COALESCE(bd.fee, 0)) > 0 THEN
            (bd.ack_amt_rmb - COALESCE(bd.fee, 0)) / bd.ack_vol
        WHEN bd.ack_amt > 0 AND bd.ack_vol > 0 THEN
            0
        ELSE
            COALESCE(nd.nav, 0)
    END as nav
FROM base_data bd
LEFT JOIN nav_data nd ON bd.fundcode = nd.fundcode AND bd.ack_dt = nd.ack_dt
) 
union
(select t5.callid::text as preId, 1  as nav
from dw.dw_trade_ic_manycall t5
where t5.call_subid::text in ('3338098','3332238','3296540','3362156','3332260','3286078','3355240','3352174','3286123','3341731')
and t5.callinfo ='1次CALL' --分次call
);