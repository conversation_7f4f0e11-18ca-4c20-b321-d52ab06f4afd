select t4.appointmentdealno as preId ,
		case when t1.ack_amt > 0 and t1.ack_vol > 0 then
            (CASE when (t1.ack_amt_rmb - coalesce(t1.fee, 0)) > 0 then
                    coalesce(case when t1.nav > 0 then 
                    				t1.nav 
                    			else 
                    				null 
                    			end, ((t1.ack_amt_rmb - coalesce(t1.fee, 0)) / t1.ack_vol)
                    		)
             ELSE 
             	0 
             end)
       else 
       	   navt.nav
       end as nav
from ods.raw_high_deal_order_dtl_high t3
inner join ods.raw_cm_zt_orderinfo t4 on t3.deal_no  = t4.dealno
inner join dw.dw_trade_mid_ack_gd t1 on t3.deal_dtl_no = t1.deal_dtl_no
--代销
and t4.appointmentdealno  in ('3338098','3332238','3296540','3362156','3332260','3286078','3355240','3352174','3286123','3341731')
left join dw.dw_pd_fund_nav navt on t3.fund_code = navt.fund_code and t3.ack_dt = to_char(navt.trade_dt, 'yyyymmdd')
