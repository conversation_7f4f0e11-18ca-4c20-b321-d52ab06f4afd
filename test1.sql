select t4.appointmentdealno as preId ,
		case when t1.ack_amt > 0 and t1.ack_vol > 0 and (t1.ack_amt_rmb - coalesce(t1.fee, 0)) > 0 and t1.nav > 0 then
        		t1.nav
        	 when t1.ack_amt > 0 and t1.ack_vol > 0 and (t1.ack_amt_rmb - coalesce(t1.fee, 0)) > 0 then
        	 	(t1.ack_amt_rmb - coalesce(t1.fee, 0)) / t1.ack_vol
        	 when t1.ack_amt > 0 and t1.ack_vol > 0 then
        	 	0
       else
       	   navt.nav
       end as nav
--代销
from ods.raw_cm_zt_orderinfo t4
inner join ods.raw_high_deal_order_dtl_high t3 on t3.deal_no  = t4.dealno
inner join dw.dw_trade_mid_ack_gd t1 on t3.deal_dtl_no = t1.deal_dtl_no
left join dw.dw_pd_fund_nav navt on t3.fund_code = navt.fund_code and t3.ack_dt = to_char(navt.trade_dt, 'yyyymmdd')
where t4.appointmentdealno  in ('3338098','3332238','3296540','3362156','3332260','3286078','3355240','3352174','3286123','3341731');



select t4.appointmentdealno as preId ,
		case when t1.ack_amt > 0 and t1.ack_vol > 0 then
            (CASE when (t1.ack_amt_rmb - coalesce(t1.fee, 0)) > 0 then
                    coalesce(case when t1.nav > 0 then
                    				t1.nav
                    			else
                    				null
                    			end, ((t1.ack_amt_rmb - coalesce(t1.fee, 0)) / t1.ack_vol)
                    		)
             ELSE
             	0
             end)
       else
       	   navt.nav
       end as nav
from ods.raw_high_deal_order_dtl_high t3
inner join ods.raw_cm_zt_orderinfo t4 on t3.deal_no  = t4.dealno
inner join dw.dw_trade_mid_ack_gd t1 on t3.deal_dtl_no = t1.deal_dtl_no
--代销
and t4.appointmentdealno  in ('3338098','3332238','3296540','3362156','3332260','3286078','3355240','3352174','3286123','3341731')
left join dw.dw_pd_fund_nav navt on t3.fund_code = navt.fund_code and t3.ack_dt = to_char(navt.trade_dt, 'yyyymmdd');


-- ========== 基于第一条SQL的优化版本（推荐使用） ==========
-- 主要优化点：
-- 1. 保持第一条SQL清晰的逻辑结构
-- 2. 使用CTE将过滤条件前置，减少JOIN数据量（从20万条减少到10条）
-- 3. 优化JOIN顺序，小表驱动大表
-- 4. 保持原有的业务逻辑完全不变

WITH filtered_orderinfo AS (
    -- 先过滤出需要的appointmentdealno，大幅减少后续JOIN的数据量
    SELECT dealno, appointmentdealno
    FROM ods.raw_cm_zt_orderinfo
    WHERE appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                               '3286078','3355240','3352174','3286123','3341731')
)
SELECT
    t4.appointmentdealno as preId,
    CASE
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 AND t1.nav > 0 THEN
            t1.nav
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 AND (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
            (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
            0
        ELSE
            navt.nav
    END as nav
FROM filtered_orderinfo t4
INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
LEFT JOIN dw.dw_pd_fund_nav navt
    ON t3.fund_code = navt.fund_code
    AND t3.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD');

