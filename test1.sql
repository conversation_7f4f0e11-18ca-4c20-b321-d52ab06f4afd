select t4.appointmentdealno as preId ,
		case when t1.ack_amt > 0 and t1.ack_vol > 0 and (t1.ack_amt_rmb - coalesce(t1.fee, 0)) > 0 and t1.nav > 0 then
        		t1.nav
        	 when t1.ack_amt > 0 and t1.ack_vol > 0 and (t1.ack_amt_rmb - coalesce(t1.fee, 0)) > 0 then
        	 	(t1.ack_amt_rmb - coalesce(t1.fee, 0)) / t1.ack_vol
        	 when t1.ack_amt > 0 and t1.ack_vol > 0 then
        	 	0
       else
       	   navt.nav
       end as nav
--代销
from ods.raw_cm_zt_orderinfo t4
inner join ods.raw_high_deal_order_dtl_high t3 on t3.deal_no  = t4.dealno
inner join dw.dw_trade_mid_ack_gd t1 on t3.deal_dtl_no = t1.deal_dtl_no
left join dw.dw_pd_fund_nav navt on t3.fund_code = navt.fund_code and t3.ack_dt = to_char(navt.trade_dt, 'yyyymmdd')
where t4.appointmentdealno  in ('3338098','3332238','3296540','3362156','3332260','3286078','3355240','3352174','3286123','3341731');


-- ========== 基于执行计划分析的优化版本（推荐） ==========
-- 执行计划问题分析：
-- 1. 总耗时：1908ms，返回10行数据
-- 2. 主要瓶颈：LEFT JOIN dw_pd_fund_nav 每次循环耗时584ms
-- 3. 分区扫描：143个分区，每次扫描68.1个分区
-- 4. 内存使用：1.3GB

WITH filtered_data AS (
    -- 第一步：完成所有INNER JOIN，获取基础数据
    SELECT
        t4.appointmentdealno as preId,
        t1.ack_amt,
        t1.ack_vol,
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav,
        t3.fund_code,
        t3.ack_dt
    FROM ods.raw_cm_zt_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
    WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                                  '3286078','3355240','3352174','3286123','3341731')
),
nav_data AS (
    -- 第二步：一次性获取所有需要的nav数据，避免重复分区扫描
    SELECT DISTINCT
        fd.fund_code,
        fd.ack_dt,
        navt.nav
    FROM filtered_data fd
    INNER JOIN dw.dw_pd_fund_nav navt
        ON fd.fund_code = navt.fund_code
        AND fd.ack_dt = TO_CHAR(navt.trade_dt, 'YYYYMMDD')
)
-- 第三步：最终计算，使用预获取的nav数据
SELECT
    fd.preId,
    CASE
        WHEN fd.ack_amt > 0 AND fd.ack_vol > 0 AND (fd.ack_amt_rmb - COALESCE(fd.fee, 0)) > 0 AND fd.nav > 0 THEN
            fd.nav
        WHEN fd.ack_amt > 0 AND fd.ack_vol > 0 AND (fd.ack_amt_rmb - COALESCE(fd.fee, 0)) > 0 THEN
            (fd.ack_amt_rmb - COALESCE(fd.fee, 0)) / fd.ack_vol
        WHEN fd.ack_amt > 0 AND fd.ack_vol > 0 THEN
            0
        ELSE
            COALESCE(nd.nav, 0)
    END as nav
FROM filtered_data fd
LEFT JOIN nav_data nd ON fd.fund_code = nd.fund_code AND fd.ack_dt = nd.ack_dt;

