-- 高级优化版本：针对大数据量的极致优化
-- 适用于：ods.raw_high_deal_order_dtl_high(40万) + ods.raw_cm_zt_orderinfo(20万) + dw.dw_trade_mid_ack_gd(40万)

-- ========== 方案一：使用EXISTS优化 ==========
-- 当只需要少量结果时，EXISTS比JOIN更高效

SELECT 
    t4.appointmentdealno as preId,
    CASE 
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
            CASE 
                WHEN (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
                    COALESCE(
                        NULLIF(t1.nav, 0),
                        (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
                    )
                ELSE 0 
            END
        ELSE COALESCE(navt.nav, 0)  -- 提供默认值避免NULL
    END as nav
FROM ods.raw_cm_zt_orderinfo t4
INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON navt.fund_code = t3.fund_code 
    AND navt.trade_dt = TO_DATE(t3.ack_dt, 'YYYYMMDD')  -- 避免字符串转换
WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                              '3286078','3355240','3352174','3286123','3341731');

-- ========== 方案二：分步查询优化（推荐用于超大数据量） ==========
/*
-- 第一步：创建临时表存储过滤结果
CREATE TEMP TABLE temp_filtered_orders AS
SELECT dealno, appointmentdealno
FROM ods.raw_cm_zt_orderinfo 
WHERE appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                           '3286078','3355240','3352174','3286123','3341731');

-- 第二步：创建索引
CREATE INDEX idx_temp_filtered_orders_dealno ON temp_filtered_orders(dealno);

-- 第三步：主查询
SELECT 
    t4.appointmentdealno as preId,
    CASE 
        WHEN t1.ack_amt > 0 AND t1.ack_vol > 0 THEN
            CASE 
                WHEN (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) > 0 THEN
                    COALESCE(
                        NULLIF(t1.nav, 0),
                        (t1.ack_amt_rmb - COALESCE(t1.fee, 0)) / t1.ack_vol
                    )
                ELSE 0 
            END
        ELSE COALESCE(navt.nav, 0)
    END as nav
FROM temp_filtered_orders t4
INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
LEFT JOIN dw.dw_pd_fund_nav navt 
    ON navt.fund_code = t3.fund_code 
    AND navt.trade_dt = TO_DATE(t3.ack_dt, 'YYYYMMDD');

-- 清理临时表
DROP TABLE temp_filtered_orders;
*/

-- ========== 方案三：使用窗口函数优化（如果需要排序或分组） ==========
/*
WITH ranked_data AS (
    SELECT 
        t4.appointmentdealno as preId,
        t1.ack_amt,
        t1.ack_vol, 
        t1.ack_amt_rmb,
        t1.fee,
        t1.nav as t1_nav,
        navt.nav as nav_value,
        ROW_NUMBER() OVER (PARTITION BY t4.appointmentdealno ORDER BY t3.ack_dt DESC) as rn
    FROM ods.raw_cm_zt_orderinfo t4
    INNER JOIN ods.raw_high_deal_order_dtl_high t3 ON t3.deal_no = t4.dealno
    INNER JOIN dw.dw_trade_mid_ack_gd t1 ON t3.deal_dtl_no = t1.deal_dtl_no
    LEFT JOIN dw.dw_pd_fund_nav navt 
        ON navt.fund_code = t3.fund_code 
        AND navt.trade_dt = TO_DATE(t3.ack_dt, 'YYYYMMDD')
    WHERE t4.appointmentdealno IN ('3338098','3332238','3296540','3362156','3332260',
                                  '3286078','3355240','3352174','3286123','3341731')
)
SELECT 
    preId,
    CASE 
        WHEN ack_amt > 0 AND ack_vol > 0 THEN
            CASE 
                WHEN (ack_amt_rmb - COALESCE(fee, 0)) > 0 THEN
                    COALESCE(
                        NULLIF(t1_nav, 0),
                        (ack_amt_rmb - COALESCE(fee, 0)) / ack_vol
                    )
                ELSE 0 
            END
        ELSE COALESCE(nav_value, 0)
    END as nav
FROM ranked_data 
WHERE rn = 1;  -- 只取每个appointmentdealno的最新记录
*/

-- ========== 建议的完整索引策略 ==========
/*
-- 核心索引（必须创建）
CREATE INDEX CONCURRENTLY idx_raw_cm_zt_orderinfo_appointmentdealno 
ON ods.raw_cm_zt_orderinfo(appointmentdealno);

CREATE INDEX CONCURRENTLY idx_raw_high_deal_order_dtl_high_deal_no 
ON ods.raw_high_deal_order_dtl_high(deal_no);

-- 复合索引（强烈建议）
CREATE INDEX CONCURRENTLY idx_raw_high_deal_order_dtl_high_fund_ack 
ON ods.raw_high_deal_order_dtl_high(fund_code, ack_dt);

CREATE INDEX CONCURRENTLY idx_dw_pd_fund_nav_fund_trade_dt 
ON dw.dw_pd_fund_nav(fund_code, trade_dt);

-- 覆盖索引（可选，进一步优化）
CREATE INDEX CONCURRENTLY idx_raw_cm_zt_orderinfo_cover 
ON ods.raw_cm_zt_orderinfo(appointmentdealno) INCLUDE (dealno);
*/

-- ========== 性能监控查询 ==========
/*
-- 监控查询执行情况
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE query LIKE '%appointmentdealno%'
ORDER BY total_time DESC;
*/
